@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

/* Custom CSS variables for your brand gradient */
:root {
  --brand-gradient: linear-gradient(135deg, #ffde58 0%, #ff8e6c 50%, #ba49ab 100%);
  --gradient-start: #ffde58;
  --gradient-middle: #ff8e6c;
  --gradient-end: #ba49ab;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/* Utility classes for gradient usage */
.bg-brand-gradient {
  background: var(--brand-gradient);
}

.border-brand-gradient {
  border: 2px solid transparent;
  background:
    linear-gradient(white, white) padding-box,
    var(--brand-gradient) border-box;
}

.border-brand-gradient-1 {
  border: 1px solid transparent;
  background:
    linear-gradient(white, white) padding-box,
    var(--brand-gradient) border-box;
}

.border-brand-gradient-3 {
  border: 3px solid transparent;
  background:
    linear-gradient(white, white) padding-box,
    var(--brand-gradient) border-box;
}

/* For dark backgrounds, use this variant */
.border-brand-gradient-dark {
  border: 2px solid transparent;
  background:
    linear-gradient(rgb(0 0 0), rgb(0 0 0)) padding-box,
    var(--brand-gradient) border-box;
}

/* Glassmorphic sidebar background utility */
.bg-glassmorphic {
  background: linear-gradient(
    150deg,
    rgba(255, 222, 88, 0.04) 0%,
    rgba(255, 142, 108, 0.04) 50%,
    rgba(186, 73, 171, 0.04) 100%
  );
  backdrop-filter: blur(3.5px);
  border: 1px solid rgba(255, 255, 255, 0.07);
  box-shadow: 0px 0px 22.7px 0px rgba(0, 0, 0, 0.4);
}

/* Connection line animations */
@keyframes dashOffset {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: 20;
  }
}

.connection-line {
  animation: dashOffset 2s linear infinite;
}

/* ReactFlow custom styles */
.react-flow__edge-path {
  stroke-dasharray: 5, 5;
  animation: dashOffset 2s linear infinite;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5, 5;
  animation: dashOffset 2s linear infinite;
}

.react-flow__handle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid #fff;
  background: #6b7280;
  transition: all 0.2s ease;
}

.react-flow__handle:hover {
  background: #ff8e6c;
  /* transform: scale(1.1); */
}

.react-flow__handle.connecting {
  background: #ff8e6c;
  transform: scale(1.2);
}

/* Controls styling */
.react-flow__controls {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.react-flow__controls-button {
  background: transparent;
  border: none;
  color: #6b7280;
  transition: color 0.2s ease;
}

.react-flow__controls-button:hover {
  color: #ff8e6c;
}

/* MiniMap styling */
.react-flow__minimap {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Background pattern */
.react-flow__background {
  background-color: #1f1f1f;
}

/* Node dragging and selection */

.react-flow__node {
  background-color: #2b2b2b;
  border: 2px solid #ffffff24;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.react-flow__node.dragging {
  opacity: 0.8;
}

.react-flow__node.selected {
  border-color: #ff8e6c;
  box-shadow: 0 0 0 2px rgba(255, 142, 108, 0.3);
}

.react-flow__node.copying {
  animation: copyPulse 0.3s ease-in-out;
}

@keyframes copyPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.react-flow__node.pasted {
  animation: pasteSlide 0.5s ease-out;
}

@keyframes pasteSlide {
  0% {
    transform: scale(0.8) translateY(-10px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Edge selection */
.react-flow__edge.selected .react-flow__edge-path {
  stroke: #ff8e6c;
  stroke-width: 3px;
}

/* Toolbar styles */
.workflow-toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

/* Modal overlay */
.modal-overlay {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.5);
}

/* Glassmorphism effect for panels */
.glass-panel {
  background: linear-gradient(
    150deg,
    rgba(255, 222, 88, 0.04) 0%,
    rgba(255, 142, 108, 0.04) 50%,
    rgba(186, 73, 171, 0.04) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Sidebar scrollbar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Node parameter form */
.node-param-form {
  max-height: 60vh;
  overflow-y: auto;
}

.node-param-form::-webkit-scrollbar {
  width: 4px;
}

.node-param-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.node-param-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.node-param-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Execution status indicators */
.execution-status {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.execution-status.running {
  background: #3b82f6;
  animation: pulse 1s infinite;
}

.execution-status.success {
  background: #10b981;
}

.execution-status.error {
  background: #ef4444;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .workflow-toolbar {
    top: 10px;
    right: 10px;
  }

  .workflow-toolbar button {
    padding: 8px 12px;
    font-size: 14px;
  }

  .sidebar {
    width: 100%;
    height: 40vh;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .react-flow__controls {
    bottom: 50vh;
  }

  .corner-gradient {
    position: relative;
    overflow: hidden; /* clip inside */
  }

  .corner-gradient::before,
  .corner-gradient::after {
    content: "";
    position: absolute;
    width: 60px; /* same size as your .size-60 */
    height: 60px;
    pointer-events: none;
    background: linear-gradient(224deg, rgba(201, 211, 238, 0), #c9d3ee, rgba(201, 211, 238, 0))
      border-box;
    border-color: transparent;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }

  /* Top-left corner */
  .corner-gradient::before {
    top: 0;
    left: 0;
    border-top: 2px solid transparent;
    border-left: 2px solid transparent;
    border-top-left-radius: 1.5rem; /* match rounded-tl-3xl */
  }

  /* Bottom-right corner */
  .corner-gradient::after {
    bottom: 0;
    right: 0;
    border-bottom: 2px solid transparent;
    border-right: 2px solid transparent;
    border-bottom-right-radius: 1.5rem; /* match rounded-br-3xl */
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom gradient text utility - placed after Tailwind layers for highest specificity */
.text-brand-gradient {
  background: linear-gradient(135deg, #ffde58 0%, #ff8e6c 50%, #ba49ab 100%) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  display: inline-block;
}
